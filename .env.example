# Environment variables for production deployment
NODE_ENV=production
PORT=3000
HOST=0.0.0.0

# Database - Update with your production database
DB_HOST=postgres
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=eu_email_webhook
DATABASE_URL=postgresql://${DB_USER}:${DB_PASSWORD}@postgres:5432/${DB_NAME}

# Redis
REDIS_URL=redis://localhost:6379

# Authentication
USER_JWT_SECRET=your-very-strong-and-secret-key-for-user-jwt
USER_JWT_EXPIRES_IN=7d

## Admin authentication
ADMIN_USERNAME=admin
ADMIN_PASSWORD=changeme # IMPORTANT: Use a strong, hashed password in production
ADMIN_EMAIL=<EMAIL>
ADMIN_JWT_SECRET=another-super-secret-admin-jwt-key-change-this
ADMIN_JWT_EXPIRES_IN=1h

# Email processing limits
MAX_EMAIL_SIZE_MB=25
WEBHOOK_TIMEOUT_MS=30000
WEBHOOK_RETRY_ATTEMPTS=3

# DNS verification settings
DNS_VERIFICATION_TIMEOUT_MS=5000
DNS_VERIFICATION_CACHE_TTL_MS=300000
DNS_VERIFICATION_RETRY_ATTEMPTS=3

# GDPR compliance settings
EMAIL_RETENTION_DAYS=30
LOG_RETENTION_DAYS=90

# Usage tracking and billing settings
DEFAULT_MONTHLY_EMAIL_LIMIT=50
FREE_PLAN_EMAIL_LIMIT=50
PRO_PLAN_EMAIL_LIMIT=1000
ENTERPRISE_PLAN_EMAIL_LIMIT=10000

# Payment processing (Mollie)
MOLLIE_API_KEY=test_dHa...
MOLLIE_WEBHOOK_SECRET=your-mollie-webhook-secret-key
MOLLIE_WEBHOOK_URL=https://yourdomain.com/api/webhooks/mollie
MOLLIE_TEST_MODE=true
MOLLIE_PROFILE_ID=pfl_...

# WebSocket Configuration
WEBSOCKET_ALLOWED_ORIGINS=https://yourdomain.com

# Attachment Processing
MAX_INLINE_ATTACHMENT_SIZE_MB=1
DEFAULT_ATTACHMENT_RETENTION_HOURS=1
PAID_ATTACHMENT_RETENTION_HOURS=24