import { simpleParser, ParsedMail, AddressObject } from 'mailparser';
import { EnhancedEmailWebhookPayload } from '../types/index.js';
import { env } from '../config/env.js';

export class EmailParser {
  static async parseToWebhookPayload(rawEmail: string | Buffer, domain: string): Promise<EnhancedEmailWebhookPayload> {
    const parsed: ParsedMail = await simpleParser(rawEmail);

    const primaryRecipient = this.extractPrimaryAddress(parsed.to) || { address: '', name: undefined };

    const payload: EnhancedEmailWebhookPayload = {
      message: {
        sender: {
          name: parsed.from?.value?.[0]?.name || null,
          email: parsed.from?.value?.[0]?.address || '',
        },
        recipient: {
          name: primaryRecipient.name || null,
          email: primaryRecipient.address,
        },
        subject: parsed.subject || null,
        content: {
          text: parsed.text || null,
          html: typeof parsed.html === 'string' ? parsed.html : null,
        },
        date: (parsed.date || new Date()).toISOString(),
        attachments: parsed.attachments?.map(att => {
          const sizeInMB = att.size / (1024 * 1024);
          const maxInlineSize = env.MAX_INLINE_ATTACHMENT_SIZE_MB;

          return {
            filename: att.filename || null,
            contentType: att.contentType,
            size: att.size,
            // Only include content for small attachments
            content: sizeInMB <= maxInlineSize ? att.content?.toString('base64') : undefined,
            // Add metadata for large attachments
            isLarge: sizeInMB > maxInlineSize,
            storageUrl: sizeInMB > maxInlineSize ? null : undefined, // Will be populated by storage service
          };
        }) || [],
      },

      envelope: {
        messageId: parsed.messageId || `generated-${Date.now()}-${Math.random()}`,
        xMailer: this.getHeader(parsed.headers, 'x-mailer'),
        deliveredTo: this.getHeader(parsed.headers, 'delivered-to'),
        xOriginalTo: this.getHeader(parsed.headers, 'x-original-to'),
        returnPath: this.getHeader(parsed.headers, 'return-path'),
        allRecipients: {
          to: this.extractAddresses(parsed.to),
          cc: this.extractAddresses(parsed.cc),
          bcc: this.extractAddresses(parsed.bcc),
        },
        headers: this.extractHeaders(parsed.headers),
        processed: {
          timestamp: new Date().toISOString(),
          domain: domain,
          originalSize: Buffer.byteLength(typeof rawEmail === 'string' ? rawEmail : rawEmail.toString()),
        },
      },
    };

    return payload;
  }

  private static extractPrimaryAddress(field: AddressObject | AddressObject[] | undefined): { address: string; name?: string } | null {
    const obj = field as AddressObject;
    if (obj?.value?.length) {
      return {
        address: obj.value[0].address,
        name: obj.value[0].name,
      };
    }
    return null;
  }

  private static extractAddresses(field: AddressObject | AddressObject[] | undefined): string[] {
    const obj = field as AddressObject;
    return obj?.value?.map(addr => addr.address) || [];
  }

  private static extractHeaders(headers: Map<string, any>): Record<string, string> {
    const result: Record<string, string> = {};
    headers.forEach((value, key) => {
      if (Array.isArray(value)) {
        result[key.toLowerCase()] = value.map(v => EmailParser.stringifyHeaderValue(v)).join(', ');
      } else {
        result[key.toLowerCase()] = EmailParser.stringifyHeaderValue(value);
      }
    });
    return result;
  }

  private static getHeader(headers: Map<string, any>, key: string): string | null {
    const value = headers.get(key);
    if (value === undefined) return null;
    return Array.isArray(value)
      ? value.map(v => EmailParser.stringifyHeaderValue(v)).join(', ')
      : EmailParser.stringifyHeaderValue(value);
  }

  private static stringifyHeaderValue(value: any): string {
    if (typeof value === 'string') return value;
    if (value?.address) return value.address; // handle AddressObject
    if (typeof value === 'object') return JSON.stringify(value);
    if (value === undefined || value === null) return '';
    return String(value);
  }

  static extractDomainFromEmail(email: string): string {
    const match = email.match(/@(.+)$/);
    return match ? match[1].toLowerCase() : '';
  }
}