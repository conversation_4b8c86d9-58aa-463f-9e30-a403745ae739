<script setup lang="ts">
import { computed } from 'vue'
import DataTable from '@components/ui/DataTable.vue'
import type { TableColumn } from '@components/ui/DataTable.vue'
import type { Alias } from '@types'
import { formatWebhookUrl } from '../../utils/url'

interface Props {
  aliases: Alias[]
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
})

const emit = defineEmits<{
  toggleAlias: [aliasId: string, active: boolean]
  editAlias: [alias: <PERSON>as]
  deleteAlias: [aliasId: string, aliasEmail: string]
  viewLogs: [domainId: string, aliasId: string]
}>()

// Helper function to check if an alias can be deleted
const canDeleteAlias = (alias: <PERSON><PERSON>): boolean => {
  // Count aliases for the same domain
  const domainAliases = props.aliases.filter(a => a.domainId === alias.domainId)

  // Cannot delete if it's the only alias for the domain
  if (domainAliases.length === 1) {
    return false
  }

  // Cannot delete catch-all if it's the only alias for the domain
  if ((alias.email === '*' || alias.email.startsWith('*@')) && domainAliases.length === 1) {
    return false
  }

  return true
}

const columns: TableColumn<Alias>[] = [
  {
    key: 'email',
    label: 'Email alias',
    sortable: true,
    render: (value, row) => {
      // Extract just the alias part (before @) or show "catch-all" for wildcards
      let displayAlias: string
      if (value === '*' || value.startsWith('*@')) {
        displayAlias = 'catch-all'
      } else if (value.includes('@')) {
        displayAlias = value.split('@')[0]
      } else {
        displayAlias = value
      }

      // Disable toggle if webhook is unverified
      const isWebhookVerified = row.webhook?.verified ?? false
      const isDisabled = !isWebhookVerified
      const toggleClass = isDisabled ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'
      const title = isDisabled ? 'Verify webhook to enable this alias' : ''

      return `
        <div class="flex items-center space-x-3">
          <input type="checkbox"
                 class="toggle toggle-primary alias-status-toggle ${toggleClass}"
                 data-alias-id="${row.id}"
                 ${row.active ? 'checked' : ''}
                 ${isDisabled ? 'disabled' : ''}
                 title="${title}">
          <div class="text-sm font-medium text-base-content">${displayAlias}</div>
        </div>
      `
    }
  },
  {
    key: 'domain',
    label: 'Domain',
    sortable: true,
    render: (value) => {
      return `<div class="text-sm text-base-content">${value?.domain || value?.name || value}</div>`
    }
  },
  {
    key: 'webhook',
    label: 'Webhook',
    sortable: true,
    render: (value) => {
      if (!value?.url) return '<span class="text-base-content/60">No webhook</span>'

      const verificationBadge = value.verified
        ? '<span class="badge badge-success bg-primary/10 text-base-content badge-sm ml-2"><div class="status status-success mr-1"></div>Verified</span>'
        : '<span class="badge badge-warning badge-sm ml-2 cursor-pointer underline hover:badge-warning/80" onclick="window.openModal(\'webhook-verification\', { webhookId: \'' + value.id + '\', webhookUrl: \'' + value.url + '\' })"><div class="status status-info mr-1 animate-bounce"></div>Verify now</span>'

      const displayUrl = formatWebhookUrl(value.url, 40)

      return `
        <div class="flex items-center gap-2 tooltip" data-tip="${value.url}">
          <span class="text-sm text-base-content">${displayUrl}</span>
          ${verificationBadge}
        </div>
      `
    }
  },
  {
    key: 'actions',
    label: '',
    width: '200px',
    render: (_value, row) => {
      // Use the same logic for display name in delete confirmation
      let displayAlias: string
      if (row.email === '*' || row.email.startsWith('*@')) {
        displayAlias = 'catch-all'
      } else if (row.email.includes('@')) {
        displayAlias = row.email.split('@')[0]
      } else {
        displayAlias = row.email
      }

      const isDeletable = canDeleteAlias(row)
      const buttonClass = isDeletable
        ? 'btn btn-outline btn-error btn-xs'
        : 'btn btn-disabled btn-xs'

      const deleteAttrs = isDeletable
        ? `data-action="deleteAlias" data-alias-id="${row.id}" data-alias-name="${displayAlias}"`
        : ''
      const tooltip = isDeletable ? '' : 'title="Cannot delete the last alias for a domain"'

      return `
        <div class="flex items-center justify-end space-x-1">
          <button type="button"
                  class="btn btn-outline btn-secondary btn-xs"
                  data-action="editAlias"
                  data-alias-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </svg>
            Edit
          </button>
          <button type="button"
                  class="btn btn-outline btn-primary btn-xs"
                  data-action="viewLogs"
                  data-domain-id="${row.domainId}"
                  data-alias-id="${row.id}">
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            Logs
          </button>
          <button type="button"
                  class="${buttonClass}"
                  ${deleteAttrs}
                  ${tooltip}
                  ${isDeletable ? '' : 'disabled'}>
            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
            </svg>
            Delete
          </button>
        </div>
      `
    }
  }
]

const handleRowClick = (alias: Alias, _index: number) => {
  // Handle row click if needed
}

const handleActionClick = (event: Event) => {
  const target = event.target as HTMLElement
  const button = target.closest('button[data-action]') as HTMLButtonElement

  if (!button) return

  const action = button.dataset.action
  const domainId = button.dataset.domainId
  const aliasId = button.dataset.aliasId
  const aliasName = button.dataset.aliasName

  if (action === 'editAlias' && aliasId) {
    const alias = props.aliases.find(a => a.id === aliasId)
    if (alias) {
      emit('editAlias', alias)
    }
  } else if (action === 'viewLogs' && domainId && aliasId) {
    emit('viewLogs', domainId, aliasId)
  } else if (action === 'deleteAlias' && aliasId && aliasName) {
    emit('deleteAlias', aliasId, aliasName)
  }
}
</script>

<template>
  <div class="bg-base-100 border border-base-300 rounded-lg shadow-sm" @click="handleActionClick">
    <DataTable
      :columns="columns"
      :data="aliases"
      :loading="loading"
      empty-message="No aliases yet. Create your first alias to start receiving emails!"
      @row-click="handleRowClick"
    />
  </div>
</template>
