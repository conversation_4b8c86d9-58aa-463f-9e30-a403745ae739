<script setup lang="ts">
import { onMounted } from 'vue'
import { useMetrics } from '@composables/useMetrics'
import { useOnboarding } from '@composables/useOnboarding'

// Shared metrics data
const {
  metrics,
  isLoading,
  lastUpdated,
  quotaColor,
  quotaTextColor,
  loadMetrics,
  refreshMetrics
} = useMetrics()

const { isNewUser } = useOnboarding()

const formatTime = (date: Date) => {
  return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })
}

onMounted(() => {
  loadMetrics()
})

onMounted(() => {
  loadMetrics()
})

// Expose refresh method for parent components
defineExpose({
  refresh: refreshMetrics
})
</script>

<template>
<div class="py-4 overflow-x-hidden bg-base-100">
  <div class="px-4 mx-auto max-w-7xl sm:px-6 lg:px-8">
    <div class="flex flex-col items-center">
      <div v-if="isLoading" class="flex items-center px-4 py-2 rounded-full bg-base-200">
        <span class="loading loading-spinner loading-sm"></span>
        <span class="ml-2 text-sm text-base-content/70">Loading...</span>
      </div>

      <div v-else class="flex flex-col items-end">
        <!-- New User Onboarding Pill -->
        <div v-if="isNewUser" class="flex items-center max-w-full px-4 py-3 space-x-4 text-sm rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 sm:px-6 sm:space-x-6">
          <div class="flex items-center space-x-2">
            <svg class="w-4 h-4 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
            </svg>
            <span class="font-medium text-base-content">Setup your first domain to get started!</span>
          </div>
        </div>

        <!-- Regular Metrics Pill -->
        <div v-else class="flex items-center max-w-full px-4 py-3 space-x-4 text-sm rounded-full bg-gradient-to-r from-primary/10 to-secondary/10 border border-primary/20 sm:px-6 sm:space-x-6">
          <!-- Email count -->
          <div class="flex items-center space-x-2 tooltip tooltip-bottom" data-tip="Emails processed in the last 24 hours">
            <div class="w-2 h-2 bg-primary rounded-full"></div>
            <span class="font-medium text-base-content">{{ metrics.emails_processed_24h }}</span>
            <span class="text-base-content/70">emails last 24h</span>
          </div>

          <!-- Quota -->
          <div class="flex items-center space-x-2 tooltip tooltip-bottom" :data-tip="`Monthly email quota: ${metrics.current_month_emails} of ${metrics.monthly_email_limit} emails used`">
            <div class="w-2 h-2 rounded-full" :class="quotaColor"></div>
            <span class="font-medium" :class="quotaTextColor">{{ metrics.current_month_emails }}/{{ metrics.monthly_email_limit }}</span>
            <span class="text-base-content/70">quota</span>
          </div>

          <!-- Success -->
          <div class="flex items-center space-x-2 tooltip tooltip-bottom" data-tip="Percentage of emails successfully delivered">
            <div class="w-2 h-2 bg-success rounded-full"></div>
            <span class="font-medium text-base-content">{{ metrics.success_rate }}%</span>
            <span class="text-base-content/70">delivery success</span>
          </div>
        </div>

        <!-- last updated -->
        <div v-if="lastUpdated" class="flex items-center mt-1 text-xs text-base-content/50">
          <span>Updated {{ formatTime(lastUpdated) }}</span>
          <button
            @click="loadMetrics"
            class="ml-2 text-primary transition-colors hover:text-primary/80"
            title="Refresh metrics"
          >
            <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
          </button>
        </div>
      </div>
    </div>
  </div>
</div>
</template>
