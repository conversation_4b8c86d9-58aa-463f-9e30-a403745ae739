<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useForm } from '@composables/useForm'
import { useAliasApi, useDomainApi } from '@composables/useApi'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useMetrics } from '@composables/useMetrics'
import WebhookSelector from './WebhookSelector.vue'
import type { CreateAliasRequest, Domain, Webhook } from '@types'

interface Props {
  initialData?: Partial<CreateAliasRequest & {
    id?: string
    allowAttachments?: boolean
    includeEnvelope?: boolean
    isCatchAll?: boolean
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [alias: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateAliasRequest & {
  allowAttachments?: boolean
  includeEnvelope?: boolean
}>({
  email: props.initialData.email || '',
  domainId: props.initialData.domainId || '',
  webhookId: props.initialData.webhookId || '',
  active: props.initialData.active ?? true,
  allowAttachments: props.initialData.allowAttachments ?? false,
  includeEnvelope: props.initialData.includeEnvelope ?? true
}, [
  { name: 'email', label: 'Email Alias', type: 'email', required: true },
  { name: 'domainId', label: 'Domain', type: 'select', required: true },
  { name: 'webhookId', label: 'Webhook', type: 'select', required: true },
  { name: 'allowAttachments', label: 'Allow attachments', type: 'checkbox', required: false },
  { name: 'includeEnvelope', label: 'Include envelope data', type: 'checkbox', required: false }
])

// API setup
const { createAlias, updateAlias } = useAliasApi()
const { getDomains } = useDomainApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()

// State
const domains = ref<Domain[]>([])
const loadingDomains = ref(false)
const aliasPrefix = ref('')

// Computed
const selectedDomain = computed(() =>
  domains.value.find(d => d.id === values.domainId)
)

const fullEmail = computed(() => {
  if (aliasPrefix.value && selectedDomain.value) {
    return `${aliasPrefix.value}@${selectedDomain.value.domainName || selectedDomain.value.domain}`
  }
  return ''
})

// Methods
const loadDomains = async () => {
  loadingDomains.value = true
  try {
    const response = await getDomains() as any
    domains.value = response.domains || []
    console.log('Domains loaded:', domains.value.length, 'domains')
    console.log('Current domainId value:', values.domainId)
    console.log('Selected domain:', selectedDomain.value)
  } catch (error) {
    console.error('Failed to load domains:', error)
  } finally {
    loadingDomains.value = false
  }
}

const onWebhookCreated = (webhook: Webhook) => {
  console.log('New webhook created:', webhook)
  // The WebhookSelector component will automatically select the new webhook
}

// Initialize alias prefix based on initial data
const initializeAliasPrefix = () => {
  if (props.initialData.email) {
    if (props.initialData.isCatchAll) {
      aliasPrefix.value = 'catch-all'
    } else if (props.initialData.email.includes('@')) {
      aliasPrefix.value = props.initialData.email.split('@')[0]
    } else {
      aliasPrefix.value = props.initialData.email
    }
  }
}

const updateEmail = () => {
  if (props.isEditMode && props.initialData.isCatchAll) {
    // Don't update email for catch-all aliases in edit mode
    return
  }

  if (aliasPrefix.value && selectedDomain.value) {
    setFieldValue('email', `${aliasPrefix.value}@${selectedDomain.value.domainName || selectedDomain.value.domain}`)
  }
}

const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    let result: any

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing alias
      const updateData = {
        email: formData.email,
        webhookId: formData.webhookId,
        allowAttachments: formData.allowAttachments,
        includeEnvelope: formData.includeEnvelope
      }
      result = await updateAlias(props.initialData.id, updateData)
      emit('success', result.alias)
      refreshWithSuccess('Alias updated successfully!', 'aliases')
    } else {
      // Create mode - create new alias
      result = await createAlias(formData) as any

      // Check if webhook needs verification
      if (result.webhookNeedsVerification) {
        // Show success message with webhook verification guidance
        const message = result.aliasSetInactive
          ? 'Alias created but set to inactive. Please verify the webhook to activate and start receiving emails.'
          : 'Alias created successfully! Please verify the webhook to ensure reliable email delivery.'

        refreshWithSuccess(message, 'aliases')

        // Emit success with webhook verification info
        emit('success', {
          ...result.alias,
          webhookNeedsVerification: true,
          webhook: result.webhook
        })
      } else {
        // Standard success flow
        emit('success', result.alias)
        refreshWithSuccess('Alias created successfully!', 'aliases')
      }
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
        console.log('Metrics refreshed after alias operation')
      } catch (error) {
        console.error('Failed to refresh metrics after alias operation:', error)
      }
    }, 500)
  })
}

// Watch for domains loading to debug selection
watch([domains, () => values.domainId], ([newDomains, newDomainId]) => {
  console.log('Domains or domainId changed:', {
    domainsCount: newDomains.length,
    domainId: newDomainId,
    selectedDomain: selectedDomain.value?.domain || selectedDomain.value?.domainName
  })
}, { immediate: true })

onMounted(() => {
  console.log('AliasForm mounted with initialData:', props.initialData)
  console.log('Form values initialized:', values)
  loadDomains()
  initializeAliasPrefix()
  // WebhookSelector component handles loading webhooks
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Email Alias -->
    <div class="form-control">
      <label class="flex-col items-start label">
        <span class="text-gray-500 label-text-alt">Alias</span>
      </label>
      <div class="relative flex items-center min-w-0 bg-base-100 border border-base-300 rounded-lg focus-within:ring-2 focus-within:ring-primary focus-within:border-primary">
        <input
          v-model="aliasPrefix"
          @input="updateEmail"
          type="text"
          :placeholder="props.initialData.isCatchAll ? 'catch-all' : 'support'"
          class="w-1/3 sm:w-1/2 min-w-0 px-3 py-2 text-sm text-base-content bg-transparent border-0 placeholder:text-base-content/40 focus:outline-none focus:ring-0"
          :disabled="isEditMode"
          :readonly="props.isEditMode && props.initialData.isCatchAll"
          required
        />
        <div class="px-2 text-sm text-base-content/70 select-none">@</div>
        <select
          :value="values.domainId"
          @change="setFieldValue('domainId', ($event.target as HTMLSelectElement).value); updateEmail()"
          class="flex-1 min-w-0 py-2 pl-3 pr-8 text-sm text-base-content bg-transparent border-0 appearance-none cursor-pointer focus:outline-none focus:ring-0"
          :disabled="loadingDomains || isEditMode"
          required
        >
          <option value="">
            {{ loadingDomains ? 'Loading...' : 'Select domain...' }}
          </option>
          <option
            v-for="domain in domains"
            :key="domain.id"
            :value="domain.id"
          >
            {{ domain.domainName || domain.domain }}
          </option>
        </select>
        <svg class="absolute w-4 h-4 text-gray-400 transform -translate-y-1/2 pointer-events-none right-2 top-1/2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
        </svg>
      </div>
      <div v-if="errors.email" class="label">
        <span class="label-text-alt text-error">{{ errors.email }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">A unique alias to forward emails to your webhook</span>
      </div>
    </div>

    <!-- Webhook Selection -->
    <WebhookSelector
      :model-value="values.webhookId"
      @update:model-value="setFieldValue('webhookId', $event)"
      @webhook-created="onWebhookCreated"
      context="create-alias"
      required
    />
    <div v-if="errors.webhookId" class="label">
      <span class="label-text-alt text-error">{{ errors.webhookId }}</span>
    </div>

    <!-- Configuration Options -->
    <div class="bg-base-200/40 rounded-lg p-4 space-y-4">
      <h3 class="text-sm font-semibold text-base-content/80">Advanced configuration</h3>

      <!-- Allow Attachments -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.allowAttachments"
            @change="setFieldValue('allowAttachments', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Allow attachments</span>
            <span class="label-text-alt text-xs">Process email attachments (< 128KB for free users)</span>
          </div>
        </label>
      </div>

      <!-- Include Envelope -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.includeEnvelope"
            @change="setFieldValue('includeEnvelope', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Include envelope data</span>
            <span class="label-text-alt text-xs">Include email headers and metadata in webhook payload</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update alias' : 'Create alias')
        }}
      </button>
    </div>
  </form>
</template>