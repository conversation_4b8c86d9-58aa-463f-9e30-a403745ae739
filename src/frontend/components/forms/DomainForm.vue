<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useForm } from '@composables/useForm'
import { useDomainApi } from '@composables/useApi'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useMetrics } from '@composables/useMetrics'
import WebhookSelector from './WebhookSelector.vue'
import type { CreateDomainRequest, Webhook } from '@types'

interface Props {
  initialData?: Partial<CreateDomainRequest & {
    preselectedWebhook?: string
    id?: string
    webhookId?: string
    allowAttachments?: boolean
    includeEnvelope?: boolean
  }>
  isEditMode?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({}),
  isEditMode: false
})

const emit = defineEmits<{
  success: [domain: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateDomainRequest & {
  allowAttachments?: boolean
  includeEnvelope?: boolean
}>({
  domain: props.initialData.domain || '',
  webhookId: props.initialData.preselectedWebhook || props.initialData.webhookId || '',
  createCatchAll: props.isEditMode ? false : true, // Only for new domains
  allowAttachments: props.initialData.allowAttachments ?? false,
  includeEnvelope: props.initialData.includeEnvelope ?? true
}, [
  { name: 'domain', label: 'Domain', type: 'text', required: true },
  { name: 'webhookId', label: 'Webhook', type: 'select', required: true },
  { name: 'allowAttachments', label: 'Allow attachments', type: 'checkbox', required: false },
  { name: 'includeEnvelope', label: 'Include envelope data', type: 'checkbox', required: false }
])

// API setup
const { createDomain, updateDomain } = useDomainApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()

// Methods
const onWebhookCreated = (webhook: Webhook) => {
  console.log('New webhook created:', webhook)
  // The WebhookSelector component will automatically select the new webhook
}

const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    let result: any

    if (props.isEditMode && props.initialData.id) {
      // Edit mode - update existing domain
      const updateData = {
        webhookId: formData.webhookId,
        allowAttachments: formData.allowAttachments,
        includeEnvelope: formData.includeEnvelope
      }
      result = await updateDomain(props.initialData.id, updateData)
      emit('success', result.domain)
      refreshWithSuccess('Domain updated successfully!', 'domains')
    } else {
      // Create mode - create new domain
      result = await createDomain(formData) as any
      emit('success', result.domain)

      // Update data reactively - refresh both domains and aliases if catch-all was created
      refreshWithSuccess('Domain created successfully!', 'domains')

      // If catch-all alias was created, also refresh aliases count
      if (formData.createCatchAll && result.alias) {
        const { triggerRefresh } = useDataRefresh()
        triggerRefresh('aliases')
      }

      // Auto-open verification steps modal for new domains
      setTimeout(() => {
        const modalData: any = {
          domain: result.domain.domain,
          domainId: result.domain.id,
          verificationToken: result.domain.verificationToken,
          webhookVerified: result.webhook?.verified
        };

        // Include webhook verification data if webhook needs verification
        if (result.webhook && !result.webhook.verified && result.webhook.id) {
          modalData.webhookNeedsVerification = true;
          modalData.webhookId = result.webhook.id;
          modalData.webhookUrl = result.webhook.url;
          modalData.webhookName = result.webhook.name;
        }

        (window as any).openModal('domain-verification', modalData);
      }, 500);
    }

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
        console.log('Metrics refreshed after domain operation')
      } catch (error) {
        console.error('Failed to refresh metrics after domain operation:', error)
      }
    }, 500)
  })
}

onMounted(() => {
  // WebhookSelector component handles loading webhooks
  console.log('DomainForm mounted with initialData:', props.initialData)
  console.log('Form values initialized:', values)
  console.log('Webhook ID from form values:', values.webhookId)
  console.log('Is edit mode:', props.isEditMode)
})
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Domain Name -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Domain name</span>
      </label>
      <input
        :value="values.domain"
        @input="setFieldValue('domain', ($event.target as HTMLInputElement).value)"
        type="text"
        placeholder="example.com"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.domain }"
        :disabled="isEditMode"
        required
      />
      <div v-if="errors.domain" class="label">
        <span class="label-text-alt text-error">{{ errors.domain }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">
          {{ isEditMode ? 'Domain name cannot be changed' : 'Enter your domain without \'www\' prefix' }}
        </span>
      </div>
    </div>

    <!-- Webhook Selection -->
    <WebhookSelector
      :model-value="values.webhookId"
      @update:model-value="setFieldValue('webhookId', $event)"
      @webhook-created="onWebhookCreated"
      context="create-domain"
      required
    />
    <div v-if="errors.webhookId" class="label">
      <span class="label-text-alt text-error">{{ errors.webhookId }}</span>
    </div>

    <!-- Configuration Options -->
    <div class="bg-base-200/40 rounded-lg p-4 space-y-4">
      <h3 class="text-sm font-semibold text-base-content/80">Advanced configuration</h3>

      <!-- Allow Attachments -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.allowAttachments"
            @change="setFieldValue('allowAttachments', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Allow attachments</span>
            <span class="label-text-alt text-xs">Process email attachments (< 128KB for free users)</span>
          </div>
        </label>
      </div>

      <!-- Include Envelope -->
      <div class="form-control">
        <label class="label cursor-pointer justify-start">
          <input
            type="checkbox"
            :checked="values.includeEnvelope"
            @change="setFieldValue('includeEnvelope', ($event.target as HTMLInputElement).checked)"
            class="checkbox checkbox-primary mr-3"
          />
          <div class="flex flex-col">
            <span class="label-text">Include envelope data</span>
            <span class="label-text-alt text-xs">Include email headers and metadata in webhook payload</span>
          </div>
        </label>
      </div>
    </div>

    <!-- Form-level errors -->
    <div v-if="errors._form" class="alert alert-error">
      <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 stroke-current shrink-0" fill="none" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z" />
      </svg>
      <span>{{ errors._form }}</span>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting
          ? (isEditMode ? 'Updating...' : 'Creating...')
          : (isEditMode ? 'Update domain' : 'Create domain')
        }}
      </button>
    </div>
  </form>
</template>