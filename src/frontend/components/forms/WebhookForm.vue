<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@composables/useForm'
import { useWebhookApi } from '@composables/useApi'
import { useDataRefresh } from '@composables/useDataRefresh'
import { useMetrics } from '@composables/useMetrics'
import type { CreateWebhookRequest } from '@types'

interface Props {
  initialData?: Partial<CreateWebhookRequest & { context?: string; domainForm?: any }>
}

const props = withDefaults(defineProps<Props>(), {
  initialData: () => ({})
})

const emit = defineEmits<{
  success: [webhook: any]
  cancel: []
}>()

// Form setup
const { values, errors, isSubmitting, isValid, setFieldValue, handleSubmit } = useForm<CreateWebhookRequest>({
  name: props.initialData.name || '',
  url: props.initialData.url || '',
  description: props.initialData.description || ''
}, [
  { name: 'name', label: 'Webhook name', type: 'text', required: true, validation: { minLength: 2, maxLength: 100 } },
  { name: 'url', label: 'Webhook URL', type: 'url', required: true },
  { name: 'description', label: 'Description', type: 'text', validation: { maxLength: 500 } }
])

// API setup
const { createWebhook } = useWebhookApi()
const { refreshWithSuccess } = useDataRefresh()
const { refreshMetrics } = useMetrics()

// Methods
const onSubmit = async () => {
  await handleSubmit(async (formData) => {
    const result = await createWebhook(formData) as any
    const webhook = result.webhook

    emit('success', webhook)

    // Update data reactively instead of page refresh
    refreshWithSuccess('Webhook created successfully!', 'webhooks')

    // Force refresh metrics to update tab counts immediately
    setTimeout(async () => {
      try {
        await refreshMetrics()
        console.log('Metrics refreshed after webhook creation')
      } catch (error) {
        console.error('Failed to refresh metrics after webhook creation:', error)
      }
    }, 500)

    // Auto-open webhook verification modal for new unverified webhooks
    setTimeout(() => {
      console.log('Opening webhook verification modal for:', webhook);
      window.openModal('webhook-verification', {
        webhookId: webhook.id,
        webhookUrl: webhook.url,
        webhookName: webhook.name
      });
    }, 700)
  })
}
</script>

<template>
  <form @submit.prevent="onSubmit" class="space-y-6">
    <!-- Webhook Name -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook name</span>
      </label>
      <input
        :value="values.name"
        @input="setFieldValue('name', ($event.target as HTMLInputElement).value)"
        type="text"
        placeholder="My webhook"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.name }"
        required
      />
      <div v-if="errors.name" class="label">
        <span class="label-text-alt text-error">{{ errors.name }}</span>
      </div>
    </div>

    <!-- Webhook URL -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Webhook URL</span>
      </label>
      <input
        :value="values.url"
        @input="setFieldValue('url', ($event.target as HTMLInputElement).value)"
        type="url"
        placeholder="https://your-app.com/webhook"
        class="w-full input input-bordered"
        :class="{ 'input-error': errors.url }"
        required
      />
      <div v-if="errors.url" class="label">
        <span class="label-text-alt text-error">{{ errors.url }}</span>
      </div>
      <div class="label">
        <span class="label-text-alt text-xs">The endpoint where emails will be sent</span>
      </div>
    </div>

    <!-- Description -->
    <div class="form-control">
      <label class="label">
        <span class="label-text">Description (optional)</span>
      </label>
      <textarea
        :value="values.description"
        @input="setFieldValue('description', ($event.target as HTMLTextAreaElement).value)"
        placeholder="Brief description of this webhook..."
        rows="3"
        class="w-full textarea textarea-bordered"
        :class="{ 'textarea-error': errors.description }"
      />
      <div v-if="errors.description" class="label">
        <span class="label-text-alt text-error">{{ errors.description }}</span>
      </div>
    </div>

    <!-- Form Actions -->
    <div class="modal-action">
      <button
        type="button"
        @click="emit('cancel')"
        class="btn btn-ghost"
      >
        Cancel
      </button>
      <button
        type="submit"
        class="btn btn-primary"
        :disabled="!isValid || isSubmitting"
        :class="{ loading: isSubmitting }"
      >
        {{ isSubmitting ? 'Creating...' : 'Create webhook' }}
      </button>
    </div>
  </form>
</template>