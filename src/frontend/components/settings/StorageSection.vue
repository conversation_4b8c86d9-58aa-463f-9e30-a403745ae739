<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Storage configuration</h2>
      
      <!-- Storage Overview -->
      <div class="bg-base-200/40 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Storage usage</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Current usage</div>
            <div class="stat-value text-2xl">{{ formatBytes(storageUsage.used) }}</div>
            <div class="stat-desc">of {{ formatBytes(storageUsage.limit) }}</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Attachments stored</div>
            <div class="stat-value text-2xl">{{ storageUsage.attachmentCount }}</div>
            <div class="stat-desc">files</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Retention period</div>
            <div class="stat-value text-2xl">{{ storageUsage.retentionHours }}h</div>
            <div class="stat-desc">auto-cleanup</div>
          </div>
        </div>
      </div>

      <!-- Storage Configuration -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">Attachment processing</h3>
        
        <div class="space-y-6">
          <!-- Free User Attachment Limits -->
          <div class="bg-info/10 border border-info/20 rounded-lg p-4">
            <h4 class="text-sm font-semibold text-info mb-2">Free plan limitations</h4>
            <div class="space-y-2 text-sm text-base-content/70">
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Maximum attachment size: <strong>128KB</strong></span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Text-based files only: <strong>PDF, TXT, CSV, MD, ICS, JSON, XML</strong></span>
              </div>
              <div class="flex items-center space-x-2">
                <svg class="w-4 h-4 text-info" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                <span>Attachments included as <strong>base64</strong> in webhook payload</span>
              </div>
            </div>
          </div>

          <!-- Storage Provider (Disabled for Free Users) -->
          <div class="opacity-60">
            <label class="label">
              <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline mr-1">Pro</span>
              <span class="label-text">Storage provider</span>
            </label>
            <select disabled class="select select-bordered w-full">
              <option>No external storage (Free plan)</option>
            </select>
            <label class="label">
              <span class="label-text-alt">
                Pro users can configure S3-compatible storage for large attachments
              </span>
            </label>
          </div>

          <!-- Retention Policy (Disabled for Free Users) -->
          <div class="opacity-60">
            <label class="label">
              <span class="badge badge-success bg-primary/10 text-base-content badge-sm badge-outline mr-1">Pro</span>
              <span class="label-text">Attachment retention</span>
            </label>
            <select disabled class="select select-bordered w-full">
              <option>No retention options (Free plan)</option>
            </select>
            <label class="label">
              <span class="label-text-alt">
                Free users receive attachments directly in webhook payload - no storage needed
              </span>
            </label>
          </div>

          <!-- Save Button -->
          <div class="pt-4">
            <button
              type="button"
              @click="saveSettings"
              :disabled="saving"
              class="btn btn-primary"
            >
              {{ saving ? 'Saving...' : 'Save storage settings' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Future Features Preview -->
      <div class="bg-base-200/40 rounded-lg p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">Coming soon</h3>
        <div class="space-y-3">
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Custom S3 bucket configuration</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Domain-specific storage settings</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Alias-level attachment policies</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Advanced retention policies</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// State
const saving = ref(false)
const settings = ref({
  maxInlineSize: 1.0,
  storageProvider: 'default',
  retentionHours: 1
})

const storageUsage = ref({
  used: 0,
  limit: 100 * 1024 * 1024, // 100MB default limit
  attachmentCount: 0,
  retentionHours: 1
})

// Methods
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const saveSettings = async () => {
  saving.value = true
  try {
    // TODO: Implement actual save functionality
    console.log('Saving storage settings:', settings.value)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show success message
    alert('Storage settings saved successfully!')
  } catch (error) {
    console.error('Failed to save storage settings:', error)
    alert('Failed to save settings. Please try again.')
  } finally {
    saving.value = false
  }
}

const loadStorageUsage = async () => {
  try {
    // TODO: Implement actual API call to get storage usage
    console.log('Loading storage usage...')
    
    // Simulate API response
    storageUsage.value = {
      used: 15 * 1024 * 1024, // 15MB
      limit: 100 * 1024 * 1024, // 100MB
      attachmentCount: 42,
      retentionHours: 1
    }
  } catch (error) {
    console.error('Failed to load storage usage:', error)
  }
}

onMounted(() => {
  loadStorageUsage()
})
</script>

<style scoped>
/* Storage section specific styles */
.stat {
  padding: 1rem;
}
</style>
