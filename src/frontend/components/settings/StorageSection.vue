<template>
  <div class="card bg-base-100">
    <div class="card-body">
      <h2 class="card-title mb-6">Storage configuration</h2>
      
      <!-- Storage Overview -->
      <div class="bg-base-200/40 rounded-lg p-6 mb-6">
        <h3 class="text-lg font-semibold mb-4">Storage usage</h3>
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Current usage</div>
            <div class="stat-value text-2xl">{{ formatBytes(storageUsage.used) }}</div>
            <div class="stat-desc">of {{ formatBytes(storageUsage.limit) }}</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Attachments stored</div>
            <div class="stat-value text-2xl">{{ storageUsage.attachmentCount }}</div>
            <div class="stat-desc">files</div>
          </div>
          <div class="stat bg-base-100 rounded-lg">
            <div class="stat-title">Retention period</div>
            <div class="stat-value text-2xl">{{ storageUsage.retentionHours }}h</div>
            <div class="stat-desc">auto-cleanup</div>
          </div>
        </div>
      </div>

      <!-- Storage Configuration -->
      <div class="bg-base-200/40 rounded-lg p-6">
        <h3 class="text-lg font-semibold mb-4">Attachment processing</h3>
        
        <div class="space-y-6">
          <!-- Inline Size Limit -->
          <div>
            <label class="label">
              <span class="label-text">Inline attachment size limit</span>
              <span class="label-text-alt">Attachments smaller than this will be included in webhook payload</span>
            </label>
            <div class="flex items-center space-x-4">
              <input
                type="range"
                min="0.1"
                max="5"
                step="0.1"
                v-model="settings.maxInlineSize"
                class="range range-primary flex-1"
              >
              <span class="text-sm font-medium min-w-[4rem]">{{ settings.maxInlineSize }} MB</span>
            </div>
          </div>

          <!-- Storage Provider -->
          <div>
            <label class="label">
              <span class="label-text">Storage provider</span>
            </label>
            <select v-model="settings.storageProvider" class="select select-bordered w-full">
              <option value="default">Default</option>
              <option value="user_s3" disabled>Custom S3 bucket (Coming Soon)</option>
            </select>
            <label class="label">
              <span class="label-text-alt">
                Large attachments will be stored here and provided as download URLs
              </span>
            </label>
          </div>

          <!-- Retention Policy -->
          <div>
            <label class="label">
              <span class="label-text">Attachment retention</span>
            </label>
            <select v-model="settings.retentionHours" class="select select-bordered w-full">
              <option value="1">1 hour (Free)</option>
              <option value="24" disabled>24 hours (Pro Plan)</option>
              <option value="168" disabled>7 days (Enterprise)</option>
              <option value="720" disabled>30 days (Enterprise)</option>
            </select>
            <label class="label">
              <span class="label-text-alt">
                How long attachments are kept before automatic deletion
              </span>
            </label>
          </div>

          <!-- Save Button -->
          <div class="pt-4">
            <button
              type="button"
              @click="saveSettings"
              :disabled="saving"
              class="btn btn-primary"
            >
              {{ saving ? 'Saving...' : 'Save storage settings' }}
            </button>
          </div>
        </div>
      </div>

      <!-- Future Features Preview -->
      <div class="bg-base-200/40 rounded-lg p-6 mt-6">
        <h3 class="text-lg font-semibold mb-4">Coming soon</h3>
        <div class="space-y-3">
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Custom S3 bucket configuration</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Domain-specific storage settings</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Alias-level attachment policies</span>
          </div>
          <div class="flex items-center space-x-3 opacity-60">
            <input type="checkbox" disabled class="checkbox checkbox-primary">
            <span>Advanced retention policies</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// State
const saving = ref(false)
const settings = ref({
  maxInlineSize: 1.0,
  storageProvider: 'default',
  retentionHours: 1
})

const storageUsage = ref({
  used: 0,
  limit: 100 * 1024 * 1024, // 100MB default limit
  attachmentCount: 0,
  retentionHours: 1
})

// Methods
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

const saveSettings = async () => {
  saving.value = true
  try {
    // TODO: Implement actual save functionality
    console.log('Saving storage settings:', settings.value)

    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))

    // Show success message
    alert('Storage settings saved successfully!')
  } catch (error) {
    console.error('Failed to save storage settings:', error)
    alert('Failed to save settings. Please try again.')
  } finally {
    saving.value = false
  }
}

const loadStorageUsage = async () => {
  try {
    // TODO: Implement actual API call to get storage usage
    console.log('Loading storage usage...')
    
    // Simulate API response
    storageUsage.value = {
      used: 15 * 1024 * 1024, // 15MB
      limit: 100 * 1024 * 1024, // 100MB
      attachmentCount: 42,
      retentionHours: 1
    }
  } catch (error) {
    console.error('Failed to load storage usage:', error)
  }
}

onMounted(() => {
  loadStorageUsage()
})
</script>

<style scoped>
/* Storage section specific styles */
.stat {
  padding: 1rem;
}
</style>
